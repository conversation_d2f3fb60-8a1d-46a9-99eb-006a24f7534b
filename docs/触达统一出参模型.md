# 触达统一出参模型设计

## 1. 设计目标

将现有的四种触达方式的出参统一为一个标准的TouchResponse模型：

| 触达方式 | 现有出参类型 | 出参含义 |
|---------|-------------|----------|
| T0普通触达 | `ImmutableTriple<Integer, EventPushBatchDo, Boolean>` | 发送数量、批次记录、是否限流 |
| T0引擎触达 | `int` | -999:流控错误, -1:无需发送, 其他:发送数量 |
| 离线引擎触达 | `int` | -999:流控错误, -1:无需发送, 其他:发送数量 |
| 离线普通触达 | `ImmutablePair<Integer, CrowdPushBatchDo>` | 发送数量、批次记录 |

## 2. 核心模型设计

### 2.1 TouchResponse - 统一触达响应模型

```java
public class TouchResponse {
    // ===== 基础响应信息 =====
    private String requestId;                    // 请求唯一标识
    private TouchType touchType;                 // 触达类型
    private TouchMode touchMode;                 // 触达模式
    private Long timestamp;                      // 响应时间戳
    private String traceId;                      // 链路追踪ID
    
    // ===== 执行结果信息 =====
    private TouchResultCode resultCode;          // 统一结果码
    private String resultMessage;                // 结果描述信息
    private boolean success;                     // 是否成功
    
    // ===== 发送统计信息 =====
    private TouchSendStatistics sendStatistics; // 发送统计信息
    
    // ===== 批次信息 =====
    private TouchBatchInfo batchInfo;            // 批次信息（单用户和批量通用）
    
    // ===== 流控信息 =====
    private TouchFlowControlInfo flowControlInfo; // 流控信息
    
    // ===== 用户结果信息 =====
    private TouchUserResult userResult;          // 单用户结果（单用户模式）
    private List<TouchUserResult> userResults;   // 用户结果列表（批量模式）
    
    // ===== 渠道特定信息 =====
    private TouchChannel channel;                // 触达渠道
    private Map<String, Object> channelResponse; // 渠道特定响应数据
    
    // ===== 扩展信息 =====
    private Map<String, Object> extData;         // 扩展数据
    
    // ===== 兼容性字段 (用于灰度切换) =====
    private Object originalResponse;             // 原始响应对象（用于兼容现有调用方）
}
```

### 2.2 关键枚举定义

#### TouchResultCode - 统一结果码
```java
public enum TouchResultCode {
    SUCCESS(0, "成功"),
    FLOW_CONTROL_ERROR(-999, "发生流控错误"),
    NO_NEED_SEND(-1, "无需发送"),
    PARTIAL_SUCCESS(1, "部分成功"),
    FAILED(-2, "发送失败"),
    RATE_LIMIT_ERROR(-998, "限流错误"),
    SYSTEM_ERROR(-500, "系统错误"),
    INVALID_PARAM(-400, "参数错误");
    
    private final int code;
    private final String message;
}
```

### 2.3 支撑模型

#### TouchSendStatistics - 发送统计信息
```java
public class TouchSendStatistics {
    private int totalCount;          // 总数量
    private int successCount;        // 成功数量
    private int failedCount;         // 失败数量
    private int filteredCount;       // 过滤数量
    private int flowControlCount;    // 流控数量
    private int skipCount;           // 跳过数量
}
```

#### TouchBatchInfo - 批次信息
```java
public class TouchBatchInfo {
    private String batchNum;         // 批次号
    private String innerBatchNum;    // 内部批次号
    private Long batchId;            // 批次ID
    private String tableName;        // 存储表名
    private LocalDateTime batchTime; // 批次时间
    private BatchStatus batchStatus; // 批次状态
}
```

#### TouchFlowControlInfo - 流控信息
```java
public class TouchFlowControlInfo {
    private boolean isFlowControlled;    // 是否被流控
    private String flowControlReason;    // 流控原因
    private String flowControlRule;      // 触发的流控规则
    private LocalDateTime flowControlTime; // 流控时间
}
```

#### TouchUserResult - 用户触达结果
```java
public class TouchUserResult {
    private Long userId;             // 用户ID
    private String mobile;           // 手机号
    private String app;              // 应用标识
    private TouchResultCode resultCode; // 用户级别结果码
    private String resultMessage;    // 结果消息
    private boolean success;         // 是否成功
    private Map<String, Object> userData; // 用户相关数据
}
```

## 3. 详细出参映射说明

### 3.1 T0普通触达 (execSend) 出参映射

**原始返回值：**
```java
ImmutableTriple<Integer, EventPushBatchDo, Boolean> execSend(...)
// Integer: 发送数量
// EventPushBatchDo: 事件推送批次记录
// Boolean: 是否发生限流
```

**映射到TouchResponse：**

| 原始字段 | TouchResponse字段 | 映射逻辑 |
|---------|------------------|----------|
| Integer (发送数量) | sendStatistics.successCount | 直接映射 |
| EventPushBatchDo | batchInfo + originalResponse | 转换为TouchBatchInfo，保留原对象 |
| Boolean (是否限流) | flowControlInfo.isFlowControlled | 直接映射 |
| - | resultCode | 根据发送数量和限流状态确定 |
| - | success | 发送数量 > 0 且未限流 |
| - | touchType | REALTIME_NORMAL |
| - | touchMode | SINGLE |

### 3.2 T0引擎触达 + 离线引擎触达 (marketingSend) 出参映射

**原始返回值：**
```java
int marketingSend(...)
// -999: 发生流控错误
// -1: 无需发送  
// 其他值: 表示发送的数量
```

**映射到TouchResponse：**

| 原始值 | TouchResponse字段 | 映射逻辑 |
|-------|------------------|----------|
| -999 | resultCode = FLOW_CONTROL_ERROR | 流控错误 |
| -1 | resultCode = NO_NEED_SEND | 无需发送 |
| > 0 | resultCode = SUCCESS, sendStatistics.successCount = 返回值 | 发送成功 |
| 0 | resultCode = FAILED | 发送失败 |
| - | touchType | 根据bizEventVO是否为null判断：null=OFFLINE_ENGINE, 非null=REALTIME_ENGINE |
| - | touchMode | SINGLE |

### 3.3 离线普通触达 (dispatchHandler) 出参映射

**原始返回值：**
```java
ImmutablePair<Integer, CrowdPushBatchDo> dispatchHandler(...)
// Integer: 发送成功数量
// CrowdPushBatchDo: 人群推送批次记录
```

**映射到TouchResponse：**

| 原始字段 | TouchResponse字段 | 映射逻辑 |
|---------|------------------|----------|
| Integer (成功数量) | sendStatistics.successCount | 直接映射 |
| CrowdPushBatchDo | batchInfo + originalResponse | 转换为TouchBatchInfo，保留原对象 |
| - | resultCode | 根据成功数量确定：>0=SUCCESS, =0=FAILED |
| - | success | successCount > 0 |
| - | touchType | OFFLINE_NORMAL |
| - | touchMode | BATCH |

## 4. 响应转换器设计

### 4.1 TouchResponseConverter

提供双向转换功能：**原始出参 ↔ TouchResponse**

#### 4.1.1 原始出参 → TouchResponse 转换方法

```java
@Component
public class TouchResponseConverter {

    /**
     * 转换T0普通触达响应
     */
    public TouchResponse convertFromExecSend(
        ImmutableTriple<Integer, EventPushBatchDo, Boolean> execSendResult,
        TouchRequest originalRequest) {

        TouchResponse response = new TouchResponse();
        response.setRequestId(originalRequest.getRequestId());
        response.setTouchType(TouchType.REALTIME_NORMAL);
        response.setTouchMode(TouchMode.SINGLE);
        response.setTimestamp(System.currentTimeMillis());
        response.setTraceId(originalRequest.getTraceId());

        // 发送统计
        TouchSendStatistics statistics = new TouchSendStatistics();
        statistics.setSuccessCount(execSendResult.getLeft());
        statistics.setTotalCount(1);
        response.setSendStatistics(statistics);

        // 流控信息
        TouchFlowControlInfo flowControlInfo = new TouchFlowControlInfo();
        flowControlInfo.setFlowControlled(execSendResult.getRight());
        response.setFlowControlInfo(flowControlInfo);

        // 批次信息
        if (execSendResult.getMiddle() != null) {
            response.setBatchInfo(convertEventPushBatchDo(execSendResult.getMiddle()));
        }

        // 结果码
        if (execSendResult.getRight()) {
            response.setResultCode(TouchResultCode.RATE_LIMIT_ERROR);
            response.setSuccess(false);
        } else if (execSendResult.getLeft() > 0) {
            response.setResultCode(TouchResultCode.SUCCESS);
            response.setSuccess(true);
        } else {
            response.setResultCode(TouchResultCode.FAILED);
            response.setSuccess(false);
        }

        // 保留原始响应
        response.setOriginalResponse(execSendResult);

        return response;
    }
    
    /**
     * 转换T0引擎触达响应
     */
    public TouchResponse convertFromT0MarketingSend(
        int marketingSendResult,
        TouchRequest originalRequest) {
        
        TouchResponse response = new TouchResponse();
        response.setRequestId(originalRequest.getRequestId());
        response.setTouchType(TouchType.REALTIME_ENGINE);
        response.setTouchMode(TouchMode.SINGLE);
        response.setTimestamp(System.currentTimeMillis());
        response.setTraceId(originalRequest.getTraceId());
        
        // 发送统计
        TouchSendStatistics statistics = new TouchSendStatistics();
        statistics.setTotalCount(1);
        
        // 根据返回值设置结果
        if (marketingSendResult == -999) {
            response.setResultCode(TouchResultCode.FLOW_CONTROL_ERROR);
            response.setSuccess(false);
            statistics.setFlowControlCount(1);
        } else if (marketingSendResult == -1) {
            response.setResultCode(TouchResultCode.NO_NEED_SEND);
            response.setSuccess(true);
            statistics.setSkipCount(1);
        } else if (marketingSendResult > 0) {
            response.setResultCode(TouchResultCode.SUCCESS);
            response.setSuccess(true);
            statistics.setSuccessCount(marketingSendResult);
        } else {
            response.setResultCode(TouchResultCode.FAILED);
            response.setSuccess(false);
            statistics.setFailedCount(1);
        }
        
        response.setSendStatistics(statistics);
        response.setOriginalResponse(marketingSendResult);
        
        return response;
    }
    
    /**
     * 转换离线引擎触达响应
     */
    public TouchResponse convertFromOfflineMarketingSend(
        int marketingSendResult,
        TouchRequest originalRequest) {
        
        TouchResponse response = convertFromT0MarketingSend(marketingSendResult, originalRequest);
        response.setTouchType(TouchType.OFFLINE_ENGINE);
        return response;
    }
    
    /**
     * 转换离线普通触达响应
     */
    public TouchResponse convertFromDispatchHandler(
        ImmutablePair<Integer, CrowdPushBatchDo> dispatchHandlerResult,
        TouchRequest originalRequest) {
        
        TouchResponse response = new TouchResponse();
        response.setRequestId(originalRequest.getRequestId());
        response.setTouchType(TouchType.OFFLINE_NORMAL);
        response.setTouchMode(TouchMode.BATCH);
        response.setTimestamp(System.currentTimeMillis());
        response.setTraceId(originalRequest.getTraceId());
        
        // 发送统计
        TouchSendStatistics statistics = new TouchSendStatistics();
        statistics.setSuccessCount(dispatchHandlerResult.getLeft());
        statistics.setTotalCount(originalRequest.getUserList() != null ? 
            originalRequest.getUserList().size() : 1);
        statistics.setFailedCount(statistics.getTotalCount() - statistics.getSuccessCount());
        response.setSendStatistics(statistics);
        
        // 批次信息
        if (dispatchHandlerResult.getRight() != null) {
            response.setBatchInfo(convertCrowdPushBatchDo(dispatchHandlerResult.getRight()));
        }
        
        // 结果码
        if (dispatchHandlerResult.getLeft() > 0) {
            response.setResultCode(TouchResultCode.SUCCESS);
            response.setSuccess(true);
        } else {
            response.setResultCode(TouchResultCode.FAILED);
            response.setSuccess(false);
        }
        
        // 保留原始响应
        response.setOriginalResponse(dispatchHandlerResult);
        
        return response;
    }
    
    // ===== TouchResponse → 原始出参 转换方法 =====

    /**
     * 转换TouchResponse到T0普通触达原始出参
     */
    public ImmutableTriple<Integer, EventPushBatchDo, Boolean> convertToExecSendResult(
        TouchResponse touchResponse) {

        // 优先使用原始响应对象
        if (touchResponse.getOriginalResponse() instanceof ImmutableTriple) {
            return (ImmutableTriple<Integer, EventPushBatchDo, Boolean>) touchResponse.getOriginalResponse();
        }

        // 从TouchResponse重构原始响应
        Integer successCount = touchResponse.getSendStatistics() != null ?
            touchResponse.getSendStatistics().getSuccessCount() : 0;

        EventPushBatchDo eventPushBatchDo = null;
        if (touchResponse.getBatchInfo() != null) {
            eventPushBatchDo = reconstructEventPushBatchDo(touchResponse.getBatchInfo());
        }

        Boolean isFlowControlled = touchResponse.getFlowControlInfo() != null ?
            touchResponse.getFlowControlInfo().isFlowControlled() : false;

        return ImmutableTriple.of(successCount, eventPushBatchDo, isFlowControlled);
    }

    /**
     * 转换TouchResponse到T0引擎触达原始出参
     */
    public Integer convertToT0MarketingSendResult(TouchResponse touchResponse) {

        // 优先使用原始响应对象
        if (touchResponse.getOriginalResponse() instanceof Integer) {
            return (Integer) touchResponse.getOriginalResponse();
        }

        // 从TouchResponse重构原始响应
        TouchResultCode resultCode = touchResponse.getResultCode();
        if (resultCode == null) {
            return 0; // 默认失败
        }

        switch (resultCode) {
            case FLOW_CONTROL_ERROR:
                return -999;
            case NO_NEED_SEND:
                return -1;
            case SUCCESS:
                return touchResponse.getSendStatistics() != null ?
                    touchResponse.getSendStatistics().getSuccessCount() : 1;
            case PARTIAL_SUCCESS:
                return touchResponse.getSendStatistics() != null ?
                    touchResponse.getSendStatistics().getSuccessCount() : 1;
            default:
                return 0; // 失败情况
        }
    }

    /**
     * 转换TouchResponse到离线引擎触达原始出参
     */
    public Integer convertToOfflineMarketingSendResult(TouchResponse touchResponse) {
        // 离线引擎触达与T0引擎触达使用相同的返回值格式
        return convertToT0MarketingSendResult(touchResponse);
    }

    /**
     * 转换TouchResponse到离线普通触达原始出参
     */
    public ImmutablePair<Integer, CrowdPushBatchDo> convertToDispatchHandlerResult(
        TouchResponse touchResponse) {

        // 优先使用原始响应对象
        if (touchResponse.getOriginalResponse() instanceof ImmutablePair) {
            return (ImmutablePair<Integer, CrowdPushBatchDo>) touchResponse.getOriginalResponse();
        }

        // 从TouchResponse重构原始响应
        Integer successCount = touchResponse.getSendStatistics() != null ?
            touchResponse.getSendStatistics().getSuccessCount() : 0;

        CrowdPushBatchDo crowdPushBatchDo = null;
        if (touchResponse.getBatchInfo() != null) {
            crowdPushBatchDo = reconstructCrowdPushBatchDo(touchResponse.getBatchInfo());
        }

        return ImmutablePair.of(successCount, crowdPushBatchDo);
    }

    // ===== 辅助转换方法 =====

    // TouchBatchInfo → 原始批次对象转换
    private EventPushBatchDo reconstructEventPushBatchDo(TouchBatchInfo batchInfo) {
        EventPushBatchDo eventPushBatchDo = new EventPushBatchDo();
        eventPushBatchDo.setBatchNum(batchInfo.getBatchNum());
        eventPushBatchDo.setInnerBatchNum(batchInfo.getInnerBatchNum());
        eventPushBatchDo.setTableName(batchInfo.getTableName());
        // TODO: 设置其他必要字段
        return eventPushBatchDo;
    }

    private CrowdPushBatchDo reconstructCrowdPushBatchDo(TouchBatchInfo batchInfo) {
        CrowdPushBatchDo crowdPushBatchDo = new CrowdPushBatchDo();
        crowdPushBatchDo.setBatchNum(batchInfo.getBatchNum());
        // TODO: 设置其他必要字段
        return crowdPushBatchDo;
    }

    // 原始批次对象 → TouchBatchInfo转换
    private TouchBatchInfo convertEventPushBatchDo(EventPushBatchDo eventPushBatchDo) {
        TouchBatchInfo batchInfo = new TouchBatchInfo();
        batchInfo.setBatchNum(eventPushBatchDo.getBatchNum());
        batchInfo.setInnerBatchNum(eventPushBatchDo.getInnerBatchNum());
        batchInfo.setTableName(eventPushBatchDo.getTableName());
        batchInfo.setBatchId(eventPushBatchDo.getId());
        // TODO: 设置其他字段
        return batchInfo;
    }

    private TouchBatchInfo convertCrowdPushBatchDo(CrowdPushBatchDo crowdPushBatchDo) {
        TouchBatchInfo batchInfo = new TouchBatchInfo();
        batchInfo.setBatchNum(crowdPushBatchDo.getBatchNum());
        batchInfo.setBatchId(crowdPushBatchDo.getId());
        // TODO: 设置其他字段
        return batchInfo;
    }
}
```

#### 4.1.2 反向转换使用示例

```java
// 示例1: 统一接口调用后转换为execSend格式
TouchResponse unifiedResponse = unifiedTouchService.processTouch(request);
ImmutableTriple<Integer, EventPushBatchDo, Boolean> execSendResult =
    touchResponseConverter.convertToExecSendResult(unifiedResponse);

// 示例2: 统一接口调用后转换为marketingSend格式
TouchResponse unifiedResponse = unifiedTouchService.processTouch(request);
Integer marketingSendResult =
    touchResponseConverter.convertToT0MarketingSendResult(unifiedResponse);

// 示例3: 统一接口调用后转换为dispatchHandler格式
TouchResponse unifiedResponse = unifiedTouchService.processTouch(request);
ImmutablePair<Integer, CrowdPushBatchDo> dispatchHandlerResult =
    touchResponseConverter.convertToDispatchHandlerResult(unifiedResponse);
```

## 5. 统一服务接口响应

### 5.1 UnifiedTouchService响应接口

```java
public interface UnifiedTouchService {
    TouchResponse processTouch(TouchRequest request);           // 同步处理
    TouchResponse processTouchAsync(TouchRequest request);      // 异步处理
    TouchResponse queryTouchProgress(String requestId);        // 查询进度
    boolean cancelTouch(String requestId);                     // 取消处理
}
```

## 6. 设计优势

### 6.1 统一性
- **统一响应结构**: 所有触达方式使用相同的TouchResponse模型
- **统一结果码**: TouchResultCode明确标识不同执行结果
- **统一统计信息**: TouchSendStatistics提供完整的发送统计

### 6.2 完整性
- **全面的结果信息**: 包含成功/失败数量、流控信息、批次信息等
- **详细的错误信息**: 明确的错误码和错误描述
- **可追踪性**: 完整的请求ID和链路追踪支持

### 6.3 兼容性
- **向下兼容**: 通过originalResponse字段保留原始响应对象
- **渐进迁移**: 可以逐步迁移现有触达方式的调用方
- **灰度切换**: 支持新旧接口并存的灰度切换场景

### 6.4 扩展性
- **模式支持**: 同时支持单用户和批量两种模式
- **渠道扩展**: channelResponse支持渠道特定的响应数据
- **业务扩展**: extData支持业务特定的扩展信息

## 7. 实施路径

### 7.1 第一阶段：模型定义
- ✅ 定义统一响应模型（TouchResponse、TouchResultCode等）
- ✅ 创建响应转换器（TouchResponseConverter）
- ✅ 更新统一服务接口（UnifiedTouchService）

### 7.2 第二阶段：转换器实现
- 实现TouchResponseConverter具体转换逻辑
- 完善EventPushBatchDo和CrowdPushBatchDo的转换方法
- 实现响应验证和错误处理

### 7.3 第三阶段：服务集成
- 在UnifiedTouchService中集成响应转换器
- 实现统一的响应处理逻辑
- 添加响应监控和日志记录

### 7.4 第四阶段：渐进迁移
- 在现有触达方法中集成响应转换器
- 提供兼容性包装方法
- 逐步迁移调用方到统一响应模型

## 8. 响应处理策略

### 8.1 错误处理策略

#### 8.1.1 系统级错误
- **网络异常**: 返回SYSTEM_ERROR，保留异常信息
- **参数错误**: 返回INVALID_PARAM，提供详细错误描述
- **服务不可用**: 返回SYSTEM_ERROR，记录服务状态

#### 8.1.2 业务级错误
- **流控错误**: 返回FLOW_CONTROL_ERROR，记录流控规则
- **限流错误**: 返回RATE_LIMIT_ERROR，提供限流原因
- **无需发送**: 返回NO_NEED_SEND，说明跳过原因

### 8.2 批量处理策略

#### 8.2.1 部分成功处理
```java
// 当批量处理中部分用户成功、部分用户失败时
if (successCount > 0 && failedCount > 0) {
    response.setResultCode(TouchResultCode.PARTIAL_SUCCESS);
    response.setSuccess(true); // 有成功的认为整体成功
    response.setResultMessage("部分用户处理成功");
}
```

#### 8.2.2 用户级别结果
```java
// 为每个用户提供详细的处理结果
List<TouchUserResult> userResults = new ArrayList<>();
for (CrowdDetailDo user : userList) {
    TouchUserResult userResult = new TouchUserResult();
    userResult.setUserId(user.getUserId());
    userResult.setSuccess(/* 根据实际处理结果 */);
    userResult.setResultCode(/* 用户级别的结果码 */);
    userResults.add(userResult);
}
response.setUserResults(userResults);
```

## 9. 监控和日志

### 9.1 响应监控指标

#### 9.1.1 成功率监控
- **整体成功率**: success字段统计
- **渠道成功率**: 按channel分组统计
- **触达类型成功率**: 按touchType分组统计

#### 9.1.2 性能监控
- **响应时间**: timestamp字段记录
- **吞吐量**: 按时间窗口统计处理数量
- **错误率**: 各种错误码的分布统计

### 9.2 日志记录策略

#### 9.2.1 结构化日志
```java
log.info("TouchResponse: requestId={}, touchType={}, resultCode={}, " +
         "successCount={}, totalCount={}, duration={}ms",
         response.getRequestId(),
         response.getTouchType(),
         response.getResultCode(),
         response.getSendStatistics().getSuccessCount(),
         response.getSendStatistics().getTotalCount(),
         response.getTimestamp() - request.getTimestamp());
```

#### 9.2.2 错误日志
```java
if (!response.isSuccess()) {
    log.warn("TouchFailed: requestId={}, touchType={}, resultCode={}, " +
             "resultMessage={}, traceId={}",
             response.getRequestId(),
             response.getTouchType(),
             response.getResultCode(),
             response.getResultMessage(),
             response.getTraceId());
}
```

## 10. 兼容性保证

### 10.1 双重兼容性策略

#### 10.1.1 方式一：原始响应对象保留（推荐）
```java
// 现有调用方可以继续使用原始响应对象
TouchResponse unifiedResponse = unifiedTouchService.processTouch(request);

// execSend兼容性
ImmutableTriple<Integer, EventPushBatchDo, Boolean> execSendResult =
    (ImmutableTriple<Integer, EventPushBatchDo, Boolean>) unifiedResponse.getOriginalResponse();

// marketingSend兼容性
Integer marketingSendResult = (Integer) unifiedResponse.getOriginalResponse();

// dispatchHandler兼容性
ImmutablePair<Integer, CrowdPushBatchDo> dispatchHandlerResult =
    (ImmutablePair<Integer, CrowdPushBatchDo>) unifiedResponse.getOriginalResponse();
```

#### 10.1.2 方式二：反向转换器（备用方案）
```java
// 当原始响应对象不可用时，使用反向转换器
TouchResponse unifiedResponse = unifiedTouchService.processTouch(request);

// execSend兼容性
ImmutableTriple<Integer, EventPushBatchDo, Boolean> execSendResult =
    touchResponseConverter.convertToExecSendResult(unifiedResponse);

// marketingSend兼容性
Integer marketingSendResult =
    touchResponseConverter.convertToT0MarketingSendResult(unifiedResponse);

// dispatchHandler兼容性
ImmutablePair<Integer, CrowdPushBatchDo> dispatchHandlerResult =
    touchResponseConverter.convertToDispatchHandlerResult(unifiedResponse);
```

### 10.2 渐进迁移支持

#### 10.2.1 完整包装器方法
```java
@Service
public class TouchCompatibilityService {

    @Autowired
    private UnifiedTouchService unifiedTouchService;

    @Autowired
    private TouchRequestConverter touchRequestConverter;

    @Autowired
    private TouchResponseConverter touchResponseConverter;

    /**
     * execSend方法的兼容性包装器
     */
    public ImmutableTriple<Integer, EventPushBatchDo, Boolean> execSendWrapper(
        DispatchDto dispatchDto, CrowdDetailDo crowdDetail,
        StrategyMarketChannelEnum channelEnum, StrategyMarketChannelDo channelDo,
        BizEventVO bizEvent) {

        // 转换为统一请求
        TouchRequest request = touchRequestConverter.convertFromExecSend(
            dispatchDto, crowdDetail, channelEnum, channelDo, bizEvent);

        // 调用统一服务
        TouchResponse response = unifiedTouchService.processTouch(request);

        // 转换回原始格式（优先使用原始响应对象）
        if (response.getOriginalResponse() instanceof ImmutableTriple) {
            return (ImmutableTriple<Integer, EventPushBatchDo, Boolean>) response.getOriginalResponse();
        } else {
            return touchResponseConverter.convertToExecSendResult(response);
        }
    }

    /**
     * marketingSend方法的兼容性包装器
     */
    public int marketingSendWrapper(
        DispatchDto dispatchDto, CrowdDetailDo crowdDetailDo,
        StrategyMarketChannelEnum channelEnum, String groupName,
        Map detailInfo, BizEventVO bizEventVO) {

        // 转换为统一请求
        TouchRequest request;
        if (bizEventVO != null) {
            request = touchRequestConverter.convertFromT0MarketingSend(
                dispatchDto, crowdDetailDo, channelEnum, groupName, detailInfo, bizEventVO);
        } else {
            request = touchRequestConverter.convertFromOfflineMarketingSend(
                dispatchDto, crowdDetailDo, channelEnum, groupName, detailInfo);
        }

        // 调用统一服务
        TouchResponse response = unifiedTouchService.processTouch(request);

        // 转换回原始格式
        if (response.getOriginalResponse() instanceof Integer) {
            return (Integer) response.getOriginalResponse();
        } else {
            return touchResponseConverter.convertToT0MarketingSendResult(response);
        }
    }

    /**
     * dispatchHandler方法的兼容性包装器
     */
    public ImmutablePair<Integer, CrowdPushBatchDo> dispatchHandlerWrapper(
        StrategyContext strategyContext, String app, String innerApp,
        List<CrowdDetailDo> batch, List<Object> templateParam) {

        // 转换为统一请求
        TouchRequest request = touchRequestConverter.convertFromDispatchHandler(
            strategyContext, app, innerApp, batch, templateParam);

        // 调用统一服务
        TouchResponse response = unifiedTouchService.processTouch(request);

        // 转换回原始格式
        if (response.getOriginalResponse() instanceof ImmutablePair) {
            return (ImmutablePair<Integer, CrowdPushBatchDo>) response.getOriginalResponse();
        } else {
            return touchResponseConverter.convertToDispatchHandlerResult(response);
        }
    }
}
```

## 11. 测试策略

### 11.1 单元测试

#### 11.1.1 转换器测试
```java
@Test
public void testConvertFromExecSend() {
    // 测试execSend响应转换
    ImmutableTriple<Integer, EventPushBatchDo, Boolean> execSendResult =
        ImmutableTriple.of(1, mockEventPushBatchDo, false);
    TouchRequest request = mockTouchRequest();

    TouchResponse response = touchResponseConverter.convertFromExecSend(execSendResult, request);

    assertEquals(TouchResultCode.SUCCESS, response.getResultCode());
    assertEquals(1, response.getSendStatistics().getSuccessCount());
    assertFalse(response.getFlowControlInfo().isFlowControlled());
}
```

#### 11.1.2 边界条件测试
```java
@Test
public void testMarketingSendErrorCodes() {
    TouchRequest request = mockTouchRequest();

    // 测试流控错误
    TouchResponse response1 = touchResponseConverter.convertFromT0MarketingSend(-999, request);
    assertEquals(TouchResultCode.FLOW_CONTROL_ERROR, response1.getResultCode());

    // 测试无需发送
    TouchResponse response2 = touchResponseConverter.convertFromT0MarketingSend(-1, request);
    assertEquals(TouchResultCode.NO_NEED_SEND, response2.getResultCode());
}
```

### 11.2 集成测试

#### 11.2.1 端到端测试
- 测试完整的请求-响应流程
- 验证原始响应的正确性
- 确保统一响应的完整性

#### 11.2.2 兼容性测试
- 验证现有调用方的兼容性
- 测试灰度切换场景
- 确保性能无退化

## 12. 文件结构

```
xyf-touch-service/
├── touch-domain/src/main/java/com/xinfei/touch/domain/
│   ├── model/unified/
│   │   ├── TouchResponse.java              # 统一触达响应模型
│   │   ├── TouchResultCode.java            # 统一结果码枚举
│   │   ├── TouchSendStatistics.java        # 发送统计信息模型
│   │   ├── TouchBatchInfo.java             # 批次信息模型
│   │   ├── TouchFlowControlInfo.java       # 流控信息模型
│   │   ├── TouchUserResult.java            # 用户触达结果模型
│   │   └── BatchStatus.java                # 批次状态枚举
│   ├── converter/
│   │   └── TouchResponseConverter.java     # 响应转换器
│   └── service/
│       └── UnifiedTouchService.java        # 统一触达服务接口（更新）
├── touch-application/src/test/java/
│   └── com/xinfei/touch/application/
│       └── TouchResponseConverterTest.java # 转换器测试
└── docs/
    └── 触达统一出参模型.md                 # 本设计文档
```

## 13. 实施完成情况

### 13.1 设计完成的工作

✅ **出参模型设计完成**
- 设计了完整的统一触达响应模型（TouchResponse）
- 定义了统一结果码枚举（TouchResultCode）
- 设计了完整的支撑模型（TouchSendStatistics、TouchBatchInfo等）

✅ **转换器设计完成**
- 设计了TouchResponseConverter响应转换器
- 提供了四种触达方式的响应转换方法
- 实现了原始响应的兼容性保留

✅ **兼容性设计完成**
- 通过originalResponse字段保证向下兼容
- 设计了包装器方法支持渐进迁移
- 提供了灰度切换的技术方案

### 13.2 技术特点

**统一性**: 所有触达方式使用相同的TouchResponse结构
**完整性**: 包含发送统计、批次信息、流控信息、用户结果等完整信息
**兼容性**: 保留原始响应对象，支持现有调用方无缝迁移
**扩展性**: 支持渠道特定响应和业务扩展数据
**可监控**: 提供完整的监控指标和结构化日志支持

### 13.3 后续工作

1. **实现转换器**: 完善TouchResponseConverter的具体实现逻辑
2. **集成测试**: 编写完整的单元测试和集成测试
3. **服务集成**: 在UnifiedTouchService中集成响应转换器
4. **性能验证**: 确保响应转换不会带来性能损耗
5. **生产验证**: 在生产环境验证兼容性和正确性

## 14. 总结

通过这个统一的触达出参模型设计，我们成功地：

1. **解决了出参不统一问题**: 四种触达方式现在可以使用统一的TouchResponse模型
2. **提供了完整的结果信息**: 包含发送统计、批次信息、流控信息等完整数据
3. **保证了向下兼容性**: 通过originalResponse字段保留原始响应对象
4. **支持了渐进迁移**: 可以逐步迁移现有调用方到统一响应模型
5. **建立了监控基础**: 为触达系统提供了完整的监控和日志支持

这个设计与《触达统一入参模型设计总结.md》形成完整的输入输出统一方案，为触达系统的统一化改造提供了完整的技术基础，可以有效解决现有系统维护成本高、响应格式不统一的问题。
